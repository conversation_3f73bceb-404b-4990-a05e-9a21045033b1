import { Page, Box } from "zmp-ui";
import { scanQRCode, showToast } from 'zmp-sdk/apis';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Pagination, Autoplay } from 'swiper/modules';
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/autoplay';
import StickyButtons from "../components/StickyButtons";

// Import static assets
import logoImage from '../static/logo-image.png';
import starIcon from '../static/star.png';
import presentIcon from '../static/present.png';
import clockIcon from '../static/clock.png';
import qaIcon from '../static/Q&A.png';
import qrcodeIcon from '../static/qrcode-icon.png';
import milkImage from '../static/milk.png';
import logoGoldgi from '../static/Logo-goldgi.png';
import logoPrimavita from '../static/Logo-primavita.png';
import logoCf from '../static/Logo-cf.png';
import logoHealth from '../static/Logo-health.png';
import goldgiDiaper from '../static/goldgi-diaper.png';

function HomePage() {
  // ProductSlider data
  const slides = [
    {
      id: 1,
      image: milkImage,
      alt: "Primavita Product 1"
    },
    {
      id: 2,
      image: milkImage,
      alt: "Primavita Product 2"
    },
    {
      id: 3,
      image: milkImage,
      alt: "Primavita Product 3"
    }
  ];

  // HotDealsSection data
  const hotDeals = [
    {
      id: 1,
      name: "Bỉm nhật Goldgi Premium...",
      size: "Size Newborn 20",
      currentPrice: "80.000",
      originalPrice: "100.000",
      discount: "10%",
      image: goldgiDiaper,
      isPrimary: true
    },
    {
      id: 2,
      name: "Bỉm nhật Goldgi Premium...",
      size: "Size Newborn 20",
      currentPrice: "80.000",
      originalPrice: "100.000",
      discount: "10%",
      image: goldgiDiaper,
      isPrimary: false
    },
    {
      id: 3,
      name: "Bỉm nhật Goldgi Premium...",
      size: "Size Newborn 20",
      currentPrice: "75.000",
      originalPrice: "90.000",
      discount: "15%",
      image: goldgiDiaper,
      isPrimary: true
    },
    {
      id: 4,
      name: "Bỉm nhật Goldgi Premium...",
      size: "Size Newborn 20",
      currentPrice: "85.000",
      originalPrice: "110.000",
      discount: "20%",
      image: goldgiDiaper,
      isPrimary: false
    }
  ];

  // PointsSection QR scan function
  const handleQRScan = async () => {
    try {
      const { content } = await scanQRCode({});
      console.log('QR Code scanned:', content);

      // Handle QR code content here
      // You can add logic to process different types of QR codes
      if (content) {
        // Show success message using Zalo Mini App toast
        showToast({
          message: `QR Code đã quét: ${content}`,
          type: 'success',
          duration: 3000
        });
      }
    } catch (error) {
      console.error('QR scan error:', error);
      // Handle error using Zalo Mini App toast
      showToast({
        message: 'Không thể quét QR code. Vui lòng thử lại.',
        type: 'error',
        duration: 3000
      });
    }
  };

  return (
    <Page className="min-h-screen pb-20" style={{ backgroundColor: '#FFFEF1' }}>
      {/* Header Section */}
      <Box className="flex items-center px-3 pt-6 pb-3" style={{ backgroundColor: '#FFFEF1' }}>
        {/* Logo NIT */}
        <Box className="flex-shrink-0 mr-3 w-16 h-10">
          <img
            src={logoImage}
            alt="NIT Logo"
            className="w-full h-full object-contain"
          />
        </Box>

        {/* Search Bar */}
        <Box className="flex-1">
          <div className="relative">
            <input
              type="text"
              placeholder="Bạn muốn tìm gì?"
              className="w-full pl-7 pr-3 py-1.5 rounded-full border border-gray-300 bg-white text-xs placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 shadow-sm"
            />
            <svg
              className="absolute left-2 top-1/2 transform -translate-y-1/2 w-3 h-3 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </div>
        </Box>
      </Box>

      {/* Points Section */}
      <Box className="mx-3 mt-2 mb-4">
        {/* Main Points Card */}
        <Box className="bg-white rounded-2xl shadow-lg border border-gray-100 p-3 mb-4">
          {/* Top Row - Points and QR Button */}
          <Box className="flex items-center justify-between mb-3">
            {/* Points Section */}
            <Box className="flex items-center flex-1">
              {/* Star Icon */}
              <Box className="mr-2 flex-shrink-0">
                <img src={starIcon} alt="Star" className="w-10 h-10 object-contain" />
              </Box>

              {/* Points Info */}
              <Box className="flex-1 min-w-0">
                <Box className="flex items-center">
                  <span className="text-lg font-bold text-gray-800 mr-1">1000</span>
                  <svg className="w-3 h-3 text-gray-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Box>
                <span className="text-xs text-gray-600 leading-tight">Điểm thưởng Bạn đang có</span>
              </Box>
            </Box>

            {/* QR Button */}
            <button
              onClick={handleQRScan}
              className="bg-blue-600 hover:bg-blue-700 text-white px-2 py-1.5 rounded-full flex items-center space-x-1 shadow-md transition-all duration-200 text-xs font-medium flex-shrink-0"
            >
              <img src={qrcodeIcon} alt="QR Code" className="w-4 h-4 object-contain" />
              <span className="xs:inline">Quét mã QR</span>
            </button>
          </Box>

          {/* Menu Items */}
          <Box className="grid grid-cols-4 gap-0.5">
            {/* Quản lý ưu đãi */}
            <Box className="flex flex-col items-center justify-center cursor-pointer hover:opacity-80 transition-opacity py-2 px-0.5">
              <img src={presentIcon} alt="Present" className="w-4 h-4 object-contain mb-1" />
              <span className="text-xs text-gray-700 leading-tight text-center whitespace-nowrap">Ưu đãi</span>
            </Box>

            {/* Cách kiếm điểm */}
            <Box className="flex flex-col items-center justify-center cursor-pointer hover:opacity-80 transition-opacity py-2 px-0.5">
              <img src={starIcon} alt="Star" className="w-4 h-4 object-contain mb-1" />
              <span className="text-xs text-gray-700 leading-tight text-center whitespace-nowrap">Kiếm điểm</span>
            </Box>

            {/* Lịch sử điểm */}
            <Box className="flex flex-col items-center justify-center cursor-pointer hover:opacity-80 transition-opacity py-2 px-0.5">
              <img src={clockIcon} alt="Clock" className="w-4 h-4 object-contain mb-1" />
              <span className="text-xs text-gray-700 leading-tight text-center whitespace-nowrap">Lịch sử</span>
            </Box>

            {/* Chính sách */}
            <Box className="flex flex-col items-center justify-center cursor-pointer hover:opacity-80 transition-opacity py-2 px-0.5">
              <img src={qaIcon} alt="Q&A" className="w-4 h-4 object-contain mb-1" />
              <span className="text-xs text-gray-700 leading-tight text-center whitespace-nowrap">Chính sách</span>
            </Box>
          </Box>
        </Box>
      </Box>

      {/* Product Slider Section */}
      <Box className="mx-3 mb-4">
        {/* Swiper Slider */}
        <Swiper
          modules={[Pagination, Autoplay]}
          slidesPerView={1}
          spaceBetween={0}
          grabCursor={true}
          pagination={{
            clickable: true,
            dynamicBullets: true,
          }}
          autoplay={{
            delay: 3000,
            disableOnInteraction: false,
          }}
          className="product-swiper bg-white rounded-2xl shadow-lg overflow-hidden"
        >
          {slides.map((slide) => (
            <SwiperSlide key={slide.id}>
              <Box className="w-full">
                <img
                  src={slide.image}
                  alt={slide.alt}
                  className="w-full h-auto object-contain"
                />
              </Box>
            </SwiperSlide>
          ))}
        </Swiper>
      </Box>

      {/* Brand Section */}
      <Box className="mx-3 mb-4">
        {/* Brand Cards Container */}
        <Box className="grid grid-cols-2 gap-3">
          {/* Goldgi Card */}
          <Box className="bg-white rounded-2xl shadow-lg border border-gray-100 p-3 flex items-center justify-center min-h-[70px]">
            <img
              src={logoGoldgi}
              alt="Goldgi"
              className="w-full h-auto object-contain max-h-12"
            />
          </Box>

          {/* Primavita Card */}
          <Box className="bg-white rounded-2xl shadow-lg border border-gray-100 p-3 flex items-center justify-center min-h-[70px]">
            <img
              src={logoPrimavita}
              alt="Primavita"
              className="w-full h-auto object-contain max-h-12"
            />
          </Box>

          {/* CF Card */}
          <Box className="bg-white rounded-2xl shadow-lg border border-gray-100 p-3 flex items-center justify-center min-h-[70px]">
            <img
              src={logoCf}
              alt="CF"
              className="w-full h-auto object-contain max-h-12"
            />
          </Box>

          {/* Health Card */}
          <Box className="bg-white rounded-2xl shadow-lg border border-gray-100 p-3 flex items-center justify-center min-h-[70px]">
            <img
              src={logoHealth}
              alt="Health"
              className="w-full h-auto object-contain max-h-12"
            />
          </Box>
        </Box>
      </Box>

      {/* Hot Deals Section */}
      <Box className="mx-3 mb-6">
        {/* Header Section */}
        <Box className="mb-3">
          {/* Title with decorative lines */}
          <Box className="flex items-center justify-center mb-2">
            <Box className="flex-1 h-0.5 bg-gradient-to-r from-transparent via-red-400 to-red-500 mr-3"></Box>
            <h2 className="text-lg font-bold text-blue-600 px-2 whitespace-nowrap">Săn Deal Siêu Hót</h2>
            <Box className="flex-1 h-0.5 bg-gradient-to-l from-transparent via-red-400 to-red-500 ml-3"></Box>
          </Box>

          {/* Subtitle and View All */}
          <Box className="flex items-center justify-between">
            <span className="text-gray-600 text-xs">Số lượng có hạn</span>
            <Box className="flex items-center">
              <span className="text-blue-600 text-xs font-medium mr-1">Xem tất cả</span>
              <svg
                className="w-3 h-3 text-blue-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            </Box>
          </Box>
        </Box>

        {/* Swiper Slider */}
        <Swiper
          slidesPerView={2}
          spaceBetween={12}
          grabCursor={true}
          className="hotdeals-swiper"
        >
          {hotDeals.map((product) => (
            <SwiperSlide key={product.id}>
              <Box className="bg-white rounded-2xl shadow-lg border border-gray-100 p-3">
                {/* Product Image */}
                <Box className="mb-2 flex justify-center">
                  <img
                    src={product.image}
                    alt={product.name}
                    className="w-full h-24 object-contain"
                  />
                </Box>

                {/* Product Info */}
                <Box className="mb-2">
                  <h3 className="text-blue-600 font-medium text-xs mb-1 line-clamp-2 leading-tight">
                    {product.name}
                  </h3>
                  <p className="text-gray-500 text-xs mb-1">
                    {product.size}
                  </p>

                  {/* Price Section */}
                  <Box className="mb-2">
                    <Box className="flex items-center gap-1 mb-1">
                      <span className="text-blue-600 font-bold text-sm">
                        {product.currentPrice}
                      </span>
                      <span className="bg-red-500 text-white text-xs px-1 py-0.5 rounded">
                        -{product.discount}
                      </span>
                    </Box>
                    <span className="text-gray-400 text-xs line-through">
                      {product.originalPrice} vnđ
                    </span>
                  </Box>
                </Box>

                {/* Action Button */}
                <button
                  className={`w-full py-2 px-2 rounded-full text-xs font-medium transition-all duration-200 ${
                    product.isPrimary
                      ? 'bg-blue-600 text-white hover:bg-blue-700 shadow-md'
                      : 'border-2 border-blue-600 text-blue-600 hover:bg-blue-50'
                  }`}
                >
                  Chọn sản phẩm
                </button>
              </Box>
            </SwiperSlide>
          ))}
        </Swiper>
      </Box>

      {/* Sticky Buttons */}
      <StickyButtons />
    </Page>
  );
}

export default HomePage;

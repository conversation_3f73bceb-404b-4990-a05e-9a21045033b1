// Brand color variables
:root {
  --brand-blue: #1531AD;
  --brand-blue-rgb: 21, 49, 173;
}

// Icon color utilities for brand colors
// Converts SVG icons to brand colors using CSS filters

// Brand color: #1531AD (blue)
.icon-brand-blue {
  filter: brightness(0) saturate(100%) invert(13%) sepia(100%) saturate(2000%) hue-rotate(230deg) brightness(95%) contrast(95%);
}

// Direct color classes using CSS variables
.text-brand-blue {
  color: var(--brand-blue) !important;
}

// For backgrounds
.bg-brand-blue {
  background-color: var(--brand-blue) !important;
}

// For borders
.border-brand-blue {
  border-color: var(--brand-blue) !important;
}

// For SVG fill
.fill-brand-blue {
  fill: var(--brand-blue) !important;
}

// For SVG stroke
.stroke-brand-blue {
  stroke: var(--brand-blue) !important;
}

// Hover states
.icon-brand-blue-hover {
  transition: filter 0.2s ease;

  &:hover {
    filter: brightness(0) saturate(100%) invert(13%) sepia(100%) saturate(2000%) hue-rotate(230deg) brightness(110%) contrast(105%);
  }
}

.page {
  padding: 16px 16px 96px 16px;
}

.section-container {
  padding: 16px;
  background: #ffffff;
  border-radius: 8px;
  margin-bottom: 24px;
}

.zaui-list-item {
  cursor: pointer;
}

/* Line clamp utility for text truncation */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Swiper custom styles */
.hotdeals-swiper {
  padding: 0 !important;
  overflow: visible;
}

.hotdeals-swiper .swiper-slide {
  height: auto;
}

/* Force mobile view in development */
@media (min-width: 768px) {
  body {
    background: #f5f5f5;
  }

  html {
    background: #f5f5f5;
  }

  .mobile-wrapper {
    max-width: 375px;
    margin: 0 auto;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.15);
    background: white;
    min-height: 100vh;
    position: relative;
    overflow: hidden; /* Contain sticky elements */
  }

  /* Add mobile device frame effect */
  .mobile-wrapper::before {
    content: '';
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: #333;
    border-radius: 2px;
    z-index: 1000;
  }
}

/* Sticky elements positioning */
.sticky-buttons {
  position: fixed;
  right: 16px;
  bottom: 128px;
  z-index: 1000;

  /* Ensure smooth animations */
  .transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Menu items animation */
  .opacity-0 {
    opacity: 0;
  }

  .opacity-100 {
    opacity: 1;
  }

  .translate-y-0 {
    transform: translateY(0);
  }

  .translate-y-4 {
    transform: translateY(1rem);
  }

  .pointer-events-none {
    pointer-events: none;
  }

  .pointer-events-auto {
    pointer-events: auto;
  }

  /* Rotation animation for menu button */
  .rotate-0 {
    transform: rotate(0deg);
  }

  .rotate-90 {
    transform: rotate(90deg);
  }
}

/* Mobile wrapper specific positioning */
@media (min-width: 768px) {
  .mobile-wrapper {
    position: relative;
  }

  .bottom-menu-bar {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
  }

  .mobile-wrapper .sticky-buttons {
    position: absolute;
    right: 16px;
    bottom: 128px;
    z-index: 1000;
  }

  /* Fixed bottom buttons in mobile wrapper */
  .mobile-wrapper .fixed.bottom-0 {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
  }
}

// Ticket card styles for VoucherDetailPage
.ticket-card {
  position: relative;

  // Ensure notches are properly positioned
  .absolute {
    z-index: 10;
  }

  // Vertical divider styling
  .border-l-2.border-dashed {
    border-left-style: dashed;
    border-left-width: 2px;
    border-left-color: #d1d5db;
  }
}

// Star exchange section styles
.star-badge {
  background-color: var(--brand-blue);
  color: white;
}

.discount-tag {
  border: 1px solid var(--brand-blue);
  color: var(--brand-blue);
  background-color: white;
}

.star-tag {
  border: 1px solid var(--brand-blue);
  color: var(--brand-blue);
  background-color: white;
}

.star-offer-card {
  border: 1px solid #e5e7eb;
  position: relative;

  /* Ensure proper spacing for the button */
  .absolute.bottom-4.right-2 {
    right: 8px;
    bottom: 16px;
  }
}

.star-offer-button {
  min-width: 90px;
  width: 90px;
  padding: 8px 12px;
  font-size: 11px;
  font-weight: 600;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: center;

  // Mobile responsive
  @media (max-width: 375px) {
    min-width: 75px;
    width: 75px;
    padding: 6px 8px;
    font-size: 10px;
  }
}

.star-offer-image-bg {
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.vertical-dotted-divider {
  width: 1px;
  height: 60px;
  border-left: 2px dotted #d1d5db;
  margin: 0 16px;
  flex-shrink: 0;
}

.vertical-dotted-divider {
  border-left: 2px dotted #d1d5db;
  height: 100%;
  margin: 0 16px;
}

.star-offer-image-bg {
  background-color: #f3f4f6;
  border-radius: 8px;
  padding: 8px;
}

/* Mobile responsive styles for PointsPage */
@media (max-width: 375px) {
  /* Points card responsive */
  .points-card-responsive {
    h2 {
      font-size: 2.5rem !important; // Smaller points number
    }

    p {
      font-size: 0.875rem !important; // Smaller text
    }

    button {
      padding: 0.5rem 0.75rem !important;
      font-size: 0.6875rem !important;
      line-height: 1.2 !important;
    }
  }

  /* Star offer cards responsive */
  .star-offer-card {
    padding: 0.75rem !important;

    h3 {
      font-size: 0.75rem !important;
      line-height: 1.2 !important;
    }

    p {
      font-size: 0.625rem !important;
      line-height: 1.2 !important;
    }

    .discount-tag, .star-tag {
      padding: 0.25rem 0.5rem !important;
      font-size: 0.625rem !important;
    }
  }

  /* Gift cards responsive */
  .gift-card-responsive {
    padding: 0.75rem !important;

    h3 {
      font-size: 0.625rem !important;
      line-height: 1.2 !important;
    }

    p {
      font-size: 0.5625rem !important;
      line-height: 1.2 !important;
    }

    img {
      width: 4rem !important;
      height: 4rem !important;
    }

    button {
      padding: 0.375rem 0.5rem !important;
      font-size: 0.5625rem !important;
    }
  }
}
